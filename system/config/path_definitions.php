<?php
/**
 * Path Definitions Configuration
 * 
 * This file contains all string definitions, descriptions, and templates
 * used by the path system and IDE helper generation.
 * 
 * Centralizing these definitions makes the system more maintainable
 * and allows for easy customization of descriptions and categories.
 */

return [
    /**
     * IDE Helper File Template Configuration
     */
    'ide_helper' => [
        'file_header' => [
            'title' => 'IDE Helper for Dynamically Generated Constants',
            'description' => 'This file is auto-generated to help IDEs recognize dynamically created constants.',
            'warning' => 'DO NOT EDIT THIS FILE MANUALLY - it will be overwritten.',
            'regenerate_command' => 'To regenerate: php system/generate_ide_helper.php'
        ],
        
        'safety_check' => [
            'condition' => "!defined('FS_APP_ROOT')",
            'message' => 'This file should not be executed directly.'
        ],
        
        'section_separator' => str_repeat('=', 60)
    ],

    /**
     * Constant Categories for IDE Helper Organization
     * 
     * Order matters - more specific prefixes should come first
     */
    'categories' => [
        'FS_FULL_' => 'Combo File System Paths',
        'FULL_' => 'Combo Web Paths',
        'FS_SYS_' => 'System File Paths',
        'FS_' => 'File System Paths',
        'APP_' => 'Application Paths',
        'DOC_' => 'Document Root Paths',
        'REQUEST_' => 'Request Information',
        'DOMAIN' => 'Domain Information',
        'ROUTE_' => 'Route Information',
        'USER_' => 'User Information',
        'SOURCE_' => 'HTMX Source Information',
        'HX_' => 'HTMX Headers',
        'SET_BY' => 'Path Source Information',
        'SCRIPT_' => 'Script Information',
        'PATH_' => 'Path Components',
        'TOP_LEVEL' => 'Navigation Information',
        'CURRENT_' => 'Current Context',
        'INPUT_' => 'Input Parameters',
        'SYSTEM_' => 'System Configuration'
    ],

    /**
     * Specific Constant Descriptions
     * 
     * These override any pattern-based descriptions
     */
    'descriptions' => [
        // Core Application Paths
        'FS_APP_ROOT' => 'File system path to application root directory',
        'FS_APP' => 'File system path to current application directory',
        'FS_APP_PATH' => 'File system path to current application view path',
        'APP_ROOT' => 'Web path to application root',
        'APP_PATH' => 'Current application path relative to root',
        
        // System Paths
        'FS_SYSTEM' => 'File system path to system directory',
        'FS_SYS_CLASSES' => 'File system path to system classes directory',
        'FS_SYS_FUNCTIONS' => 'File system path to system functions directory',
        'FS_SYS_VIEWS' => 'File system path to system views directory',
        'FS_SYS_CONFIG' => 'File system path to system config directory',
        'FS_SYS_TEMPLATES' => 'File system path to system templates directory',
        'FS_SYS_COMPONENTS' => 'File system path to system components directory',
        'FS_SYS_API' => 'File system path to system API directory',
        'FS_SYS_LOGS' => 'File system path to system logs directory',
        'FS_SYS_DB_CLASS' => 'File system path to database class file',
        
        // Resource Paths
        'FS_RESOURCES' => 'File system path to resources directory',
        'FS_CLASSES' => 'File system path to classes directory',
        'FS_FUNCTIONS' => 'File system path to functions directory',
        'FS_VIEWS' => 'File system path to views directory',
        'FS_CONFIG' => 'File system path to config directory',
        'FS_TEMPLATES' => 'File system path to templates directory',
        'FS_COMPONENTS' => 'File system path to components directory',
        'FS_API' => 'File system path to API directory',
        'FS_LOGS' => 'File system path to logs directory',
        'FS_UPLOADS' => 'File system path to uploads directory',
        
        // External Paths
        'FS_CACHE' => 'File system path to cache directory',
        'FS_TEMP' => 'File system path to temporary files directory',
        
        // Combo Paths
        'FS_FULL_PATH' => 'File system path combining app root and current application path',
        'FS_FULL_PAGE' => 'File system path to current page including full application path',
        'FULL_PATH' => 'Web path combining app root and current application path',
        'FULL_PAGE' => 'Web path to current page including full application path',
        
        // Request and Navigation
        'REQUEST_URI' => 'Current request URI',
        'DOMAIN' => 'Current domain name',
        'SCRIPT_NAME' => 'Current script filename',
        'DOC_ROOT' => 'Document root path',
        'FS_DOC_ROOT' => 'File system document root path',
        
        // Route Information
        'ROUTE_TREE' => 'Application route tree structure',
        'ROUTE_LIST' => 'Flat list of application routes',
        'ROUTES' => 'Database routes data',
        
        // User and Context
        'USER_ROLE' => 'Current user role',
        'INPUT_PARAMS' => 'Combined GET and POST parameters',
        'SYSTEM_VIEWS' => 'Array of system view names',
        
        // Path Components
        'PATH_PARTS' => 'Array of path segments',
        'TOP_LEVEL' => 'Top-level path segment',
        'CURRENT_PAGE' => 'Current page name',
        
        // HTMX and Source Information
        'SET_BY' => 'Indicates how the path was determined',
        'SOURCE_PATH' => 'HTMX source path',
        'SOURCE_PAGE' => 'HTMX source page',
        'SOURCE_PATH_PARTS' => 'HTMX source path components',
        'SOURCE_APP_PATH' => 'HTMX source application path',
        'SOURCE_APP_PATH_PARTS' => 'HTMX source application path components',
        'SOURCE_FS_PATH' => 'HTMX source file system path',
        'HX_CURRENT_URL' => 'HTMX current URL header value',
        'HX_CURRENT_URL_PARTS' => 'Parsed HTMX current URL components',

        // Additional Constants (also defined in additional_constants section)
        'DEBUG_MODE' => 'Debug mode flag for development environment',
        'API_RUN' => 'Flag indicating if running in API mode',
        'POOFED' => 'Application initialization completion flag'
    ],

    /**
     * Pattern-Based Description Templates
     * 
     * Used when no specific description is found
     * The {part} placeholder will be replaced with the extracted part
     */
    'patterns' => [
        'FS_FULL_' => 'File system combo path for {part}',
        'FULL_' => 'Web combo path for {part}',
        'FS_SYS_' => 'File system path to system {part} directory',
        'FS_' => 'File system path to {part} directory',
        'APP_' => 'Application {part} path',
        'REQUEST_' => 'Request {part} information',
        'SOURCE_' => 'HTMX source {part} information',
        'HX_' => 'HTMX {part} header',
        'ROUTE_' => 'Route {part} data',
        'USER_' => 'User {part} information',
        'PATH_' => 'Path {part} component',
        'SYSTEM_' => 'System {part} configuration'
    ],

    /**
     * Default Description Template
     * 
     * Used when no pattern matches
     */
    'default_description' => 'Dynamically generated constant from {source_key}',

    /**
     * Additional Constants for IDE Helper
     * 
     * These are constants that might be defined elsewhere but should
     * be included in the IDE helper for completeness
     */
    'additional_constants' => [
        'DEBUG_MODE' => [
            'value' => true,
            'type' => 'boolean',
            'source_key' => 'debug_mode',
            'description' => 'Debug mode flag for development environment'
        ],
        'API_RUN' => [
            'value' => false,
            'type' => 'boolean',
            'source_key' => 'api_run',
            'description' => 'Flag indicating if running in API mode'
        ],
        'POOFED' => [
            'value' => true,
            'type' => 'boolean',
            'source_key' => 'poofed',
            'description' => 'Application initialization completion flag'
        ]
    ]
];
