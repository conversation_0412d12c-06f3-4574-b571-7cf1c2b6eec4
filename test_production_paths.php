<?php
/**
 * Test script to verify that paths.php works in production mode
 * (without IDE helper functions)
 */

echo "Testing production paths (without IDE helper functions)...\n";

// Initialize path
$path = [];
$path['fs_app_root'] = __DIR__ . '/';

// Load the path schema
$schema_file = $path['fs_app_root'] . 'system/config/path_schema.php';
$schema = file_exists($schema_file) ? include($schema_file) : [];

// Include paths functions (without IDE helper functions)
include($path['fs_app_root'] . "system/paths.php");

// Build paths with schema
$path = build_paths($path, $schema);

// Test building constants without IDE helper (production mode)
echo "Building constants without IDE helper...\n";
build_constants($path, false);

// Verify some constants were created
$test_constants = ['FS_APP_ROOT', 'FS_SYSTEM', 'APP_ROOT', 'FS_FULL_PATH', 'FULL_PATH'];
$success = true;

foreach ($test_constants as $constant) {
    if (defined($constant)) {
        echo "✓ $constant: " . constant($constant) . "\n";
    } else {
        echo "✗ $constant: NOT DEFINED\n";
        $success = false;
    }
}

// Test building constants with IDE helper flag but no functions available
echo "\nTesting with IDE helper flag (should gracefully skip)...\n";
build_constants($path, true);

if ($success) {
    echo "\n✓ Production mode test PASSED - all constants created successfully!\n";
    echo "✓ IDE helper functions are not needed in production.\n";
} else {
    echo "\n✗ Production mode test FAILED - some constants missing!\n";
}

echo "\nTest completed.\n";
?>
